package routes

import (
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/domains/other"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/middleware"
	"github.com/onwa/pkg/onwalog"
	"github.com/onwa/pkg/state"
)

func OtherRoutes(r *gin.RouterGroup, s other.Service) {
	r.POST("/contact", middleware.FromClient(), middleware.Authorized(), contact(s))

	r.GET("/sss", middleware.FromClient(), middleware.Authorized(), getSSS(s))
	r.POST("/sss", middleware.FromClient(), addSSS(s))

	r.POST("/notification/webhook", middleware.ControlCore(), sendNotificationWebhook(s))
}

// @Summary Contact
// @Description Contact
// @Tags Other Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForContact true "request contact add"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /contact [POST]
func contact(s other.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var (
			req        dtos.RequestForContact
			file_names []string
		)
		if err := c.ShouldBind(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "contact",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		form, err := c.MultipartForm()
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "contact",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		files := form.File["photos"]

		if len(files) > 3 {
			onwalog.CreateLog(&entities.Log{
				Title:    "contact",
				Message:  "Error: " + "max 3 files",
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		allowedExtensions := []string{".png", ".jpeg", ".jpg"}

		for _, file := range files {
			ext := strings.ToLower(filepath.Ext(file.Filename))
			if !isAllowedExtension(ext, allowedExtensions) {
				onwalog.CreateLog(&entities.Log{
					Title:    "contact",
					Message:  "Error: " + "file extension not allowed",
					Type:     "error",
					Ip:       state.GetCurrentIP(c),
					URL:      c.Request.URL.Path,
					OS:       state.GetCurrentOS(c),
					UserID:   state.GetCurrentUserID(c),
					DeviceID: state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			}

			filename := strings.ReplaceAll(filepath.Base(file.Filename), " ", "")
			filename = filename + "-" + state.GetCurrentUserID(c).String() + "-" + uuid.NewString() + ext

			savePath := filepath.Join("uploads", filename)
			if err := c.SaveUploadedFile(file, savePath); err != nil {
				onwalog.CreateLog(&entities.Log{
					Title:    "contact",
					Message:  "Error: " + err.Error(),
					Type:     "error",
					Ip:       state.GetCurrentIP(c),
					URL:      c.Request.URL.Path,
					OS:       state.GetCurrentOS(c),
					UserID:   state.GetCurrentUserID(c),
					DeviceID: state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			}

			file_names = append(file_names, filename)
		}

		if err := s.Contact(c, req, file_names); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "contact",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:    "contact",
			Message:  "Success contact form has been sent",
			Type:     "info",
			Ip:       state.GetCurrentIP(c),
			URL:      c.Request.URL.Path,
			OS:       state.GetCurrentOS(c),
			UserID:   state.GetCurrentUserID(c),
			DeviceID: state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("process_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

// @Summary Get SSS
// @Description Get SSS
// @Tags Other Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /sss [GET]
func getSSS(s other.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetSSS(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "getSSS",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:    "getSSS",
			Message:  "Success: SSS' has been fetched",
			Type:     "info",
			Ip:       state.GetCurrentIP(c),
			URL:      c.Request.URL.Path,
			OS:       state.GetCurrentOS(c),
			UserID:   state.GetCurrentUserID(c),
			DeviceID: state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// it need force_update_key in header

// @Summary Add SSS
// @Description Add SSS
// @Tags Other Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForSSS true "request sss add"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /sss [POST]
func addSSS(s other.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForSSS
		if err := c.ShouldBind(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("create_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		force_update_key := c.Request.Header.Get("force_update_key")
		if force_update_key != config.InitConfig().App.ForceUpdateKey {
			c.AbortWithStatusJSON(401, gin.H{
				"error": "key is wrong",
			})
			return
		}

		if err := s.AddSSS(c, req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("create_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("create_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

func isAllowedExtension(ext string, allowed []string) bool {
	for _, a := range allowed {
		if ext == a {
			return true
		}
	}
	return false
}

func sendNotificationWebhook(s other.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForSendNotification
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "Send Notification Json Bind Error",
				Message:  err.Error(),
				Type:     "error",
				Ip:       "",
				URL:      c.Request.URL.Path,
				OS:       "",
				UserID:   uuid.Nil,
				DeviceID: "",
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.SendNotification(c, req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "Send Notification Error",
				Message:  err.Error(),
				Type:     "error",
				Ip:       "",
				URL:      c.Request.URL.Path,
				OS:       "",
				UserID:   uuid.Nil,
				DeviceID: "",
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   "success",
			"status": 200,
		})
	}
}
