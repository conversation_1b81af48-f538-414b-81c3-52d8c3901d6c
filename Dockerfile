FROM golang:1.24-alpine AS builder

RUN apk add --no-cache upx
RUN apk --no-cache add tzdata

WORKDIR /src

COPY . .

RUN go mod download

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o onwa main.go
RUN upx onwa


FROM scratch

# take env from build args
ARG VERSION
ENV APP_VERSION=$VERSION
ENV DEV_MODE=true

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

WORKDIR /bin/onwa

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /src/onwa .

# Copy the config file
COPY config.yaml /bin/onwa/

CMD ["./onwa"]
