// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/contact": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Contact",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Other Endpoints"
                ],
                "summary": "Contact",
                "parameters": [
                    {
                        "description": "request contact add",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForContact"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/create-invoice": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Create Invoice For Crypto Payment",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Endpoints"
                ],
                "summary": "Create Invoice For Crypto Payment",
                "parameters": [
                    {
                        "description": "request Create Invoice",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateInvoice"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/license": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Current License",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Endpoints"
                ],
                "summary": "Get Current License",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Add License",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Endpoints"
                ],
                "summary": "Add License",
                "parameters": [
                    {
                        "description": "request Lisence Add",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateLicense"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/login": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Login",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth Endpoints"
                ],
                "summary": "Login",
                "parameters": [
                    {
                        "description": "for auth",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForLogin"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/notification": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Notif Info",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Endpoints"
                ],
                "summary": "Get Notif Info",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Update Notif Info",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Endpoints"
                ],
                "summary": "Update Notif Info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Data",
                        "name": "data",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/notification/after/offline": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Update Notif After Offline",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Endpoints"
                ],
                "summary": "Update Notif After Offline",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Data",
                        "name": "data",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/sss": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get SSS",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Other Endpoints"
                ],
                "summary": "Get SSS",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Add SSS",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Other Endpoints"
                ],
                "summary": "Add SSS",
                "parameters": [
                    {
                        "description": "request sss add",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForSSS"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/user-info": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get User Detail",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth Endpoints"
                ],
                "summary": "Get User Detail",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Phone",
                        "name": "phone",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/version": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Version",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Version Endpoints"
                ],
                "summary": "Get Version",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Update Version",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Version Endpoints"
                ],
                "summary": "Update Version",
                "parameters": [
                    {
                        "description": "request update version",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateVersion"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/check-device": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Check Device is for understand its working or not",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WP Endpoints"
                ],
                "summary": "Check Device",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/phone-number": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Phone Number Get All is for getting phone numbers that you created.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "PhoneNumber Endpoints"
                ],
                "summary": "Phone Number Get All",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Phone Number Create is for creating a new phone number.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "PhoneNumber Endpoints"
                ],
                "summary": "Phone Number Create",
                "parameters": [
                    {
                        "description": "create phone number",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForWPPhoneNumbers"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/phone-number/{id}": {
            "delete": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Phone Number Delete is for deleting a phone number.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "PhoneNumber Endpoints"
                ],
                "summary": "Phone Number Delete",
                "parameters": [
                    {
                        "type": "string",
                        "description": "phone number id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/presence": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Presence Get All is for getting presences by params.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Presence Endpoints"
                ],
                "summary": "Presence Get All",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Phone",
                        "name": "phone",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/presence/start": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Presence Start is for starting a new presence.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Presence Endpoints"
                ],
                "summary": "Presence Start",
                "parameters": [
                    {
                        "description": "request start new presence",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForPresenceStart"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/presence/stop/{presence_id}": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Presence Stop is for stopping a presence.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Presence Endpoints"
                ],
                "summary": "Presence Stop",
                "parameters": [
                    {
                        "type": "string",
                        "description": "presence id",
                        "name": "presence_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/presence/{id}": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Presence Get By ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Presence Endpoints"
                ],
                "summary": "Presence Get By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Phone",
                        "name": "phone",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/profile-photo": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Profile Photo is used to get a profile photo. If you provide a phone number, the profile photo associated with that number will be returned. If no phone number is provided, the profile photo of the current session will be returned instead.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WP Endpoints"
                ],
                "summary": "Get Profile Photo",
                "parameters": [
                    {
                        "description": "request profile photo",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForProfilePhoto"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/report": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Detail Report",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Report Endpoints"
                ],
                "summary": "Get Detail Report",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Phone",
                        "name": "phone",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/report/last-presence/{presence_id}": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Presence Get Last is for getting last presence.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Presence Endpoints"
                ],
                "summary": "Presence Get Last",
                "parameters": [
                    {
                        "type": "string",
                        "description": "presence id",
                        "name": "presence_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/request-code": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Request WP Code for getting wp login code",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WP Endpoints"
                ],
                "summary": "Request WP Code",
                "parameters": [
                    {
                        "description": "request wp code",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForWPCode"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/session/create": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Session Create for creating a new session, if you have already one, we return exist one",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Session Endpoints"
                ],
                "summary": "Session Create",
                "parameters": [
                    {
                        "description": "session create",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateSession"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/wp/session/stop": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Session Stop for stopping a current session",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Session Endpoints"
                ],
                "summary": "Session Stop",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dtos.RequestForContact": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "subject": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForCreateInvoice": {
            "type": "object",
            "properties": {
                "license_type": {
                    "description": "-----\u003e 1: weekly, 2: monthly",
                    "type": "integer"
                },
                "pay_currency": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForCreateSession": {
            "type": "object",
            "required": [
                "e_164_phone_number"
            ],
            "properties": {
                "e_164_phone_number": {
                    "type": "string",
                    "example": "+***********"
                }
            }
        },
        "dtos.RequestForLogin": {
            "type": "object",
            "required": [
                "device_id",
                "phone_language",
                "purchase_id",
                "push_notif_token",
                "time_zone"
            ],
            "properties": {
                "device_id": {
                    "type": "string",
                    "example": "abc123-device-id"
                },
                "last_version_build_number": {
                    "type": "integer",
                    "example": 42
                },
                "last_version_name": {
                    "type": "string",
                    "example": "1.2.3"
                },
                "name": {
                    "type": "string",
                    "example": "John Doe"
                },
                "os": {
                    "type": "string",
                    "example": "Android"
                },
                "phone_language": {
                    "type": "string",
                    "example": "en"
                },
                "purchase_id": {
                    "type": "string",
                    "example": "purchase_456789"
                },
                "push_notif_token": {
                    "type": "string",
                    "example": "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]"
                },
                "time_zone": {
                    "type": "string",
                    "example": "Warsaw/Poland"
                }
            }
        },
        "dtos.RequestForPresenceStart": {
            "type": "object",
            "properties": {
                "contact_id": {
                    "type": "string"
                },
                "contact_name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForProfilePhoto": {
            "type": "object",
            "properties": {
                "for_session": {
                    "type": "boolean",
                    "example": true
                },
                "phone": {
                    "type": "string",
                    "example": "***********"
                }
            }
        },
        "dtos.RequestForSSS": {
            "type": "object",
            "properties": {
                "answer": {
                    "type": "string"
                },
                "question": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForUpdateLicense": {
            "type": "object",
            "properties": {
                "reason": {
                    "description": "1. for commenting 2. ad",
                    "type": "integer",
                    "example": 1
                },
                "time": {
                    "type": "integer",
                    "example": 3
                },
                "user_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForUpdateVersion": {
            "type": "object",
            "properties": {
                "android_build_number": {
                    "type": "integer"
                },
                "android_force_update_build_number": {
                    "type": "integer"
                },
                "android_version_name": {
                    "type": "string"
                },
                "ios_build_number": {
                    "type": "integer"
                },
                "ios_force_update_build_number": {
                    "type": "integer"
                },
                "ios_version_name": {
                    "type": "string"
                },
                "is_force": {
                    "type": "boolean"
                }
            }
        },
        "dtos.RequestForWPCode": {
            "type": "object",
            "required": [
                "e_164_phone_number"
            ],
            "properties": {
                "e_164_phone_number": {
                    "type": "string",
                    "example": "+***********"
                }
            }
        },
        "dtos.RequestForWPPhoneNumbers": {
            "type": "object",
            "required": [
                "phone_numbers"
            ],
            "properties": {
                "phone_numbers": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": [
                            "dial_code",
                            "phone_number",
                            "raw_phone_number"
                        ],
                        "properties": {
                            "dial_code": {
                                "type": "string",
                                "example": "90"
                            },
                            "name": {
                                "type": "string",
                                "example": "samet"
                            },
                            "phone_number": {
                                "type": "string",
                                "example": "+***********"
                            },
                            "raw_phone_number": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8000",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "Onwa API",
	Description:      "Onwa API Documentation",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
