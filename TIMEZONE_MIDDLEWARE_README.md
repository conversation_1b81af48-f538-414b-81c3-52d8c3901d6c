# Timezone Conversion Middleware

Bu middleware, API response'larındaki tüm time field'ların<PERSON> kullanıcının timezone'ına göre otomatik olarak dönüştürür.

## Özellikler

- ✅ Tüm time field'larını otomatik olarak kullanıcının timezone'ına dönüştürür
- ✅ Nested object'lerdeki time field'larını da dönüştürür
- ✅ Array içindeki time field'larını da dönüştürür
- ✅ Non-time field'ları değiştirmez
- ✅ Timezone set edilmemişse conversion yapmaz
- ✅ Geçersiz timezone'larda UTC kullanır
- ✅ JSON olmayan response'ları etkilemez

## Kurulum

Middleware zaten `pkg/middleware/middleware.go` dosyasına eklenmiştir ve `pkg/server/http.go` dosyasında API group'una uygulanmıştır.

## Kullanım

### 1. JWT Token ile Timezone

Mevcut sistemde kullanıcının timezone'u JWT token'da saklanıyor ve `Authorized()` middleware'i tarafından context'e set ediliyor:

```go
// JWT token'dan timezone'u context'e set etme
c.Set(state.CurrentTimezone, claims.Timezone)
```

### 2. Header ile Timezone (Fallback)

Eğer context'te timezone yoksa, middleware `X-Timezone` header'ından timezone'u alır:

```bash
curl -H "X-Timezone: Europe/Istanbul" http://localhost:8080/api/v1/endpoint
```

## Desteklenen Timezone Formatları

Middleware Go'nun `time.LoadLocation()` fonksiyonunu kullanır, bu nedenle tüm IANA timezone'ları desteklenir:

- `Europe/Istanbul`
- `America/New_York`
- `Asia/Tokyo`
- `UTC`
- vb.

## Örnek Kullanım

### Giriş (UTC):
```json
{
  "data": {
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T15:30:00Z",
    "nested_data": {
      "timestamp": "2024-01-01T18:45:00Z",
      "non_time_field": "this should not change"
    }
  }
}
```

### Çıkış (Europe/Istanbul - UTC+3):
```json
{
  "data": {
    "created_at": "2024-01-01T15:00:00+03:00",
    "updated_at": "2024-01-01T18:30:00+03:00",
    "nested_data": {
      "timestamp": "2024-01-01T21:45:00+03:00",
      "non_time_field": "this should not change"
    }
  }
}
```

## Test

Test dosyası `pkg/middleware/timezone_test.go` olarak oluşturulmuştur:

```bash
go test ./pkg/middleware -v
```

## Demo

Demo uygulaması `demo/timezone_demo.go` dosyasında bulunur:

```bash
go run demo/timezone_demo.go
```

Demo endpoint'leri:
- `GET /api/v1/test-timezone` - Timezone conversion ile
- `GET /api/v1/test-no-timezone` - Timezone conversion olmadan
- `GET /health` - Health check

## Desteklenen Time Formatları

Middleware aşağıdaki time formatlarını otomatik olarak tanır ve dönüştürür:

- RFC3339: `2006-01-02T15:04:05Z07:00`
- RFC3339Nano: `2006-01-02T15:04:05.999999999Z07:00`
- UTC: `2006-01-02T15:04:05Z`
- UTC with milliseconds: `2006-01-02T15:04:05.000Z`
- Simple format: `2006-01-02 15:04:05`

## Performans

Middleware sadece JSON response'ları işler ve timezone set edilmişse çalışır. Timezone set edilmemişse hiçbir işlem yapmaz ve performans etkisi yoktur.

## Güvenlik

- Geçersiz timezone'lar UTC'ye fallback eder
- JSON parsing hataları original response'u korur
- Middleware hataları original response'u etkilemez

## Entegrasyon

Middleware şu anda tüm `/api/v1/*` endpoint'lerine uygulanmıştır. Belirli endpoint'leri hariç tutmak için middleware'i route seviyesinde uygulayabilirsiniz:

```go
// Sadece belirli route'lara uygulama
api.GET("/user-info", middleware.TimezoneConverter(), getUserInfo)

// Veya belirli route'ları hariç tutma
api.GET("/raw-data", getRawData) // Timezone conversion olmadan
```
