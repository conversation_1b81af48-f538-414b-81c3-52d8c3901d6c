package cron

import (
	"log"
	"time"

	"github.com/go-co-op/gocron/v2"
	"github.com/onwa/pkg/database"
	"github.com/onwa/pkg/entities"
)

func MyCron() {
	s, _ := gocron.NewScheduler()

	j3, _ := s.<PERSON>(
		gocron.DurationJob(
			5*time.Minute,
		),
		gocron.NewTask(
			cronOfControlSession,
		),
	)
	j4, _ := s.<PERSON>(
		gocron.CronJob(
			`0 45 23 * * *`,
			true,
		),
		gocron.NewTask(
			deleteData,
		),
	)

	log.Println("j3: ", j3.ID())
	log.Println("j4: ", j4.ID())
	s.Start()
	log.Println("My Cron started...")
}

func cronOfControlSession() {
	db := database.DBClient()

	var presences []entities.Presence

	db.Model(&entities.Presence{}).
		Where("done = ?", false).
		Find(&presences)

	if len(presences) == 0 {
		return
	}

	for _, v := range presences {

		var (
			current_user    entities.User
			current_license entities.License
		)

		if err := db.Model(&entities.User{}).
			Where("id = ?", v.UserID).
			First(&current_user).Error; err != nil {
			continue
		}

		if err := db.Model(&entities.License{}).
			Where("user_id = ?", current_user.ID).
			Order("created_at DESC").
			First(&current_license).Error; err != nil {
			continue
		}

		if current_license.EndDate.Unix() < time.Now().Unix() {
			// lisansı yoksa oturumu kapat
			db.Model(&entities.Presence{}).
				Where("id = ?", v.ID).
				Updates(map[string]interface{}{
					"done":     true,
					"ended_at": time.Now(),
				})
				
		}
	}
}

// func cronOfControlSession2() {
// 	var presences []entities.Presence
// 	db := database.DBClient()
// 	db.Model(&entities.Presence{}).
// 		Where("done = ?", false).
// 		Find(&presences)

// 	for _, v := range presences {
// 		var waha_resp dtos.WahaResponseCheckStatus
// 		body, err := utils.RequestGET(fmt.Sprintf("/api/sessions/%s", v.SessionName))
// 		if err != nil {
// 			db.Model(&entities.Presence{}).
// 				Where("uuid = ?", v.UUID).
// 				Updates(map[string]interface{}{
// 					"done":     true,
// 					"ended_at": time.Now(),
// 				})
// 			continue
// 		}

// 		if err := json.Unmarshal(*body, &waha_resp); err != nil {
// 			db.Model(&entities.Presence{}).
// 				Where("uuid = ?", v.UUID).
// 				Updates(map[string]interface{}{
// 					"done":     true,
// 					"ended_at": time.Now(),
// 				})
// 			continue
// 		}

// 		if waha_resp.Status != "WORKING" {
// 			db.Model(&entities.Presence{}).
// 				Where("uuid = ?", v.UUID).
// 				Updates(map[string]interface{}{
// 					"done":     true,
// 					"ended_at": time.Now(),
// 				})
// 			continue
// 		}
// 	}
// }

func deleteData() {
	db := database.DBClient()

	db.Model(&entities.Log{}).
		Where("created_at < ?", time.Now().Add(-24*time.Hour)).Unscoped().
		Delete(&entities.Log{})

	db.Model(&entities.HttpLog{}).
		Where("created_at < ?", time.Now().Add(-24*time.Hour)).Unscoped().
		Delete(&entities.HttpLog{})

}
