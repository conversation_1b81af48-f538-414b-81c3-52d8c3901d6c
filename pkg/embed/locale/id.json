{"notification_not_online": "Tidak online dalam %v menit terakhir 😢", "notification_just_second": "Online selama {s} detik dalam {t} menit terakhir 🤔", "notification_total": "Online selama {m} menit dan {s} detik dalam {t} menit terakhir 🤔", "notification_happen": "Pengguna sekarang online", "error_should_bind_json": "<PERSON><PERSON><PERSON><PERSON>, silakan coba lagi nanti.", "error_no_data": "Data tidak ditemukan.", "error_login": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat masuk. <PERSON>lakan coba lagi.", "licence_is_not_valid": "Anda tidak memiliki langganan, silakan beli satu.", "license_error_max_person": "Maksimal %v orang dapat dilacak", "license_error_type_must_be_free": "<PERSON><PERSON> langganan harus gratis.", "license_error_only_3_ads": "Anda hanya dapat menonton 3 iklan dalam 24 jam terakhir.", "license_error_comment_already_done": "<PERSON>a sudah memberikan ulasan untuk aplikasi ini.", "phone_number_error_max_number": "<PERSON>a dapat menambahkan hingga 10 nomor telepon.", "presence_error_already_exist": "Sesi pelacakan yang ada sudah aktif.", "update_success": "Data berhasil diperbarui.", "update_error": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memperbarui data.", "delete_success": "Data berhasil dihapus.", "delete_error": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat menghapus data.", "process_success": "Proses ber<PERSON><PERSON> disel<PERSON>.", "process_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han selama proses berl<PERSON>.", "create_success": "Data berhasil dibuat.", "create_error": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat membuat data.", "get_error": "<PERSON><PERSON><PERSON><PERSON> masalah saat mengambil data. Silakan coba lagi.", "authorization_token_required_error": "<PERSON><PERSON> o<PERSON>an", "authorization_incorrect_format_error": "Format token otorisasi salah", "authorization_token_invalid_or_expired_error": "Token tidak valid atau telah kedal<PERSON>, silakan coba lagi nanti.", "unauthorized_client_error": "<PERSON><PERSON><PERSON> tidak ber<PERSON>", "mf_user_error": "Akun sudah dibuat dengan nomor telepon ini. <PERSON><PERSON>an beli langganan."}