{"notification_not_online": "In den letzten %v Minute(n) nicht online 😢", "notification_just_second": "In den letzten {t} Minute(n) für {s} Sekunde(n) online 🤔", "notification_total": "In den letzten {t} Minute(n) für {m} Minute(n) und {s} Sekunde(n) online 🤔", "notification_happen": "Der Benutzer ist jetzt online", "error_should_bind_json": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später erneut.", "error_no_data": "<PERSON><PERSON> Daten gefunden.", "error_login": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "licence_is_not_valid": "<PERSON>e haben kein Abonnement, bitte kaufen Si<PERSON> e<PERSON>.", "license_error_max_person": "Es können maximal %v Personen verfolgt werden", "license_error_type_must_be_free": "Der Abonnementtyp muss kostenlos sein.", "license_error_only_3_ads": "In den letzten 24 Stunden können nur 3 Anzeigen angesehen werden.", "license_error_comment_already_done": "Sie haben diese App bereits bewertet.", "phone_number_error_max_number": "Sie können bis zu 10 Telefonnummern hinzufügen.", "presence_error_already_exist": "Es gibt bereits eine aktive Überwachungssitzung.", "update_success": "Daten erfolgreich aktualisiert.", "update_error": "Beim Aktualisieren der Daten ist ein Fehler aufgetreten.", "delete_success": "Daten erfolgreich gelö<PERSON>t.", "delete_error": "Beim Löschen der Daten ist ein Fehler aufgetreten.", "process_success": "Der Vorgang wurde erfolgreich abgeschlossen.", "process_error": "Während des Vorgangs ist ein Fehler aufgetreten.", "create_success": "Daten erfolgreich erstellt.", "create_error": "<PERSON><PERSON> der Daten ist ein Fehler aufgetreten.", "get_error": "<PERSON>im Abrufen der Daten ist ein Problem aufgetreten. Bitte versuchen Sie es erneut.", "authorization_token_required_error": "Autorisierungstoken ist erforderlich", "authorization_incorrect_format_error": "Das Format des Autorisierungstokens ist falsch", "authorization_token_invalid_or_expired_error": "Das Token ist ungültig oder abgelaufen, bitte versuchen Sie es später erneut.", "unauthorized_client_error": "Unbefugter Client", "mf_user_error": "<PERSON><PERSON> dieser Telefonnummer wurde bereits ein Konto erstellt. Bitte kaufen Sie ein Abonnement."}