{"notification_not_online": "Non è stato online negli ultimi %v minuto/i 😢", "notification_just_second": "Online per {s} secondo/i negli ultimi {t} minuto/i 🤔", "notification_total": "Online per {m} minuto/i e {s} secondo/i negli ultimi {t} minuto/i 🤔", "notification_happen": "L'utente è ora online", "error_should_bind_json": "Si è verificato un errore, riprova più tardi.", "error_no_data": "<PERSON><PERSON><PERSON> dato trovato.", "error_login": "Si è verificato un errore durante l'accesso. Riprova.", "licence_is_not_valid": "Non hai un abbonamento, acquistane uno.", "license_error_max_person": "È possibile monitorare un massimo di %v persone", "license_error_type_must_be_free": "Il tipo di abbonamento deve essere gratuito.", "license_error_only_3_ads": "<PERSON><PERSON><PERSON> guardare solo 3 annunci nelle ultime 24 ore.", "license_error_comment_already_done": "Hai già lasciato un commento per questa applicazione.", "phone_number_error_max_number": "Puoi aggiungere fino a 10 numeri di telefono.", "presence_error_already_exist": "Esiste già una sessione di monitoraggio attiva.", "update_success": "<PERSON>ti aggiornati con successo.", "update_error": "Si è verificato un errore durante l'aggiornamento dei dati.", "delete_success": "Dati eliminati con successo.", "delete_error": "Si è verificato un errore durante l'eliminazione dei dati.", "process_success": "Il processo è stato completato con successo.", "process_error": "Si è verificato un errore durante il processo.", "create_success": "<PERSON><PERSON> creati con successo.", "create_error": "Si è verificato un errore durante la creazione dei dati.", "get_error": "Si è verificato un problema durante il recupero dei dati. Riprova.", "authorization_token_required_error": "È richiesto un token di autorizzazione", "authorization_incorrect_format_error": "Il formato del token di autorizzazione è errato", "authorization_token_invalid_or_expired_error": "Il token non è valido o è scaduto, riprova più tardi.", "unauthorized_client_error": "Client non autorizzato", "mf_user_error": "Un account è già stato creato con questo numero di telefono. Acquista un abbonamento."}