package middleware

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/database"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/state"
	"github.com/onwa/pkg/utils"
	"gorm.io/gorm"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func FromClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("client_id")
		if client_id == config.InitConfig().App.ClientID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("unauthorized_client_error", c.<PERSON>(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func ControlCore() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("onwa_id")
		if client_id == config.InitConfig().App.OnwaID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("unauthorized_client_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}

		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		token := strings.Split(bearer, "Bearer ")[1]

		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.UserID)
			c.Set(state.CurrentDeviceID, claims.DeviceID)
			c.Set(state.CurrentPushNotifToken, claims.PushNotifToken)
			c.Set(state.CurrentPurchaseID, claims.PurchaseID)
			c.Set(state.CurrentRegID, claims.RegID)
			c.Set(state.CurrentPhoneNumber, claims.E164PhoneNumber)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Set(state.CurrentOS, claims.OS)

			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

	}
}

func LicenseControl() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			current_license   entities.License
			current_presences []entities.Presence
			//current_presences_count int
		)

		tx := database.DBClient().Begin()

		if err := tx.Model(&entities.License{}).
			Where("user_id = ?", c.GetString(state.CurrentUserID)).
			Order("created_at DESC").
			First(&current_license).Error; err != nil {
			tx.Rollback()
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("licence_is_not_valid", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		if err := tx.Model(&entities.Presence{}).
			Where("user_id = ?", c.GetString(state.CurrentUserID)).
			Where("done = ?", false).
			Find(&current_presences).Error; err != nil && err != gorm.ErrRecordNotFound {
			tx.Rollback()
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("licence_is_not_valid", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		tx.Commit()

		//current_presences_count = len(current_presences)

		if current_license.LicenseType == 1 {
			//-----> Free License
			if current_license.EndDate.Unix() < time.Now().Unix() {
				if current_license.TotalTime > 0 {
					// if current_presences_count >= 1 {
					// 	c.AbortWithStatusJSON(401, gin.H{"error": fmt.Sprintf(localizer.GetTranslated("license_error_max_person", c.GetString(state.CurrentPhoneLanguage), nil), 1)})
					// } else {
					c.Next()
					// }
				} else {
					c.AbortWithStatusJSON(401, gin.H{"error": localizer.GetTranslated("licence_is_not_valid", c.GetString(state.CurrentPhoneLanguage), nil)})
				}
			} else {
				// if current_presences_count >= 1 {
				// 	c.AbortWithStatusJSON(401, gin.H{"error": fmt.Sprintf(localizer.GetTranslated("license_error_max_person", c.GetString(state.CurrentPhoneLanguage), nil), 1)})
				// } else {
				c.Next()
				// }
			}
		} else {
			//-----> Pre License
			if current_license.EndDate.Unix() > time.Now().Unix() {
				// if current_presences_count >= 3 {
				// 	c.AbortWithStatusJSON(401, gin.H{"error": fmt.Sprintf(localizer.GetTranslated("license_error_max_person", c.GetString(state.CurrentPhoneLanguage), nil), 3)})
				// } else {
				c.Next()
				// }
			} else {
				c.AbortWithStatusJSON(401, gin.H{"error": localizer.GetTranslated("licence_is_not_valid", c.GetString(state.CurrentPhoneLanguage), nil)})
			}
		}
	}
}

// responseWriter wraps gin.ResponseWriter to capture response data
type responseWriter struct {
	gin.ResponseWriter
	body   *bytes.Buffer
	status int
}

func (w *responseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return len(data), nil // Don't write to original writer yet
}

func (w *responseWriter) WriteHeader(status int) {
	w.status = status
}

// TimezoneConverter middleware converts all time fields in response to user's timezone
func TimezoneConverter() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user's timezone from context or header
		userTimezone := c.GetString(state.CurrentTimezone)
		if userTimezone == "" {
			// Try to get from header as fallback
			userTimezone = c.GetHeader("X-Timezone")
		}

		if userTimezone == "" {
			c.Next()
			return // If no timezone is set, don't convert
		}

		// Parse the timezone
		loc, err := time.LoadLocation(userTimezone)
		if err != nil {
			// If timezone is invalid, use UTC
			loc = time.UTC
		}

		// Wrap the response writer
		rw := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBuffer(nil),
			status:         200, // Default status
		}
		c.Writer = rw

		c.Next()

		// Get the response body
		responseBody := rw.body.String()
		if responseBody == "" {
			// Write original response if empty
			rw.ResponseWriter.WriteHeader(rw.status)
			return
		}

		// Parse JSON response
		var responseData any
		if err := json.Unmarshal([]byte(responseBody), &responseData); err != nil {
			// If it's not JSON, write original response
			rw.ResponseWriter.WriteHeader(rw.status)
			rw.ResponseWriter.Write(rw.body.Bytes())
			return
		}

		// Convert time fields recursively
		convertedData := convertTimeFields(responseData, loc)

		// Marshal back to JSON
		convertedJSON, err := json.Marshal(convertedData)
		if err != nil {
			// If marshaling fails, write original response
			rw.ResponseWriter.WriteHeader(rw.status)
			rw.ResponseWriter.Write(rw.body.Bytes())
			return
		}

		// Write the converted response
		rw.ResponseWriter.Header().Set("Content-Type", "application/json; charset=utf-8")
		rw.ResponseWriter.WriteHeader(rw.status)
		rw.ResponseWriter.Write(convertedJSON)
	}
}

// convertTimeFields recursively converts time fields in the data structure
func convertTimeFields(data any, loc *time.Location) any {
	switch v := data.(type) {
	case map[string]any:
		result := make(map[string]any)
		for key, value := range v {
			result[key] = convertTimeFields(value, loc)
		}
		return result
	case []any:
		result := make([]any, len(v))
		for i, value := range v {
			result[i] = convertTimeFields(value, loc)
		}
		return result
	case string:
		// Try to parse as RFC3339 time format (Go's default JSON time format)
		if t, err := time.Parse(time.RFC3339, v); err == nil {
			return t.In(loc).Format(time.RFC3339)
		}
		// Try to parse as RFC3339Nano format
		if t, err := time.Parse(time.RFC3339Nano, v); err == nil {
			return t.In(loc).Format(time.RFC3339Nano)
		}
		// Try to parse common time formats
		timeFormats := []string{
			"2006-01-02T15:04:05Z07:00",
			"2006-01-02T15:04:05.000Z07:00",
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.000Z",
			"2006-01-02 15:04:05",
		}
		for _, format := range timeFormats {
			if t, err := time.Parse(format, v); err == nil {
				return t.In(loc).Format(time.RFC3339)
			}
		}
		return v
	default:
		return v
	}
}
