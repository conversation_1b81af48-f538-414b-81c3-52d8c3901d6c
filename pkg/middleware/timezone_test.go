package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/state"
)

func TestTimezoneConverter(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a new Gin router
	router := gin.New()
	router.Use(TimezoneConverter())

	// Create a test route
	router.GET("/test", func(c *gin.Context) {
		c.Set(state.CurrentTimezone, "Europe/Istanbul")
		c.JSON(200, map[string]any{
			"data": map[string]any{
				"last_connection_check_time": "2024-01-01T12:00:00Z",
				"created_at":                 "2024-01-01T10:30:00Z",
			},
			"status": 200,
		})
	})

	// Create a test request
	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(w, req)

	// Check if we got a response
	if w.Code != 200 {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	// Parse the response
	var response map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Errorf("Failed to parse response: %v", err)
	}

	// Check if the response has the expected structure
	data, ok := response["data"].(map[string]any)
	if !ok {
		t.Error("Response should have a 'data' field")
	}

	// Check if time fields exist
	if _, exists := data["last_connection_check_time"]; !exists {
		t.Error("Response should have 'last_connection_check_time' field")
	}
}

func TestConvertTimeFields(t *testing.T) {
	// Test timezone
	loc, _ := time.LoadLocation("Europe/Istanbul")

	// Test converting a time string
	input := "2024-01-01T12:00:00Z"
	result := convertTimeFields(input, loc)

	if result == nil {
		t.Error("Result should not be nil")
	}

	// Test converting a non-time string
	nonTimeInput := "not a time string"
	nonTimeResult := convertTimeFields(nonTimeInput, loc)

	if nonTimeResult != nonTimeInput {
		t.Error("Non-time strings should remain unchanged")
	}

	// Test converting a nested object
	nestedInput := map[string]any{
		"user": map[string]any{
			"created_at": "2024-01-01T12:00:00Z",
			"name":       "test",
		},
	}

	nestedResult := convertTimeFields(nestedInput, loc)
	if nestedResult == nil {
		t.Error("Nested result should not be nil")
	}
}
