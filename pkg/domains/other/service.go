package other

import (
	"context"

	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
)

type Service interface {
	Contact(ctx context.Context, req dtos.RequestForContact, file_names []string) error

	GetSSS(ctx context.Context) ([]entities.SSS, error)
	AddSSS(ctx context.Context, req dtos.RequestForSSS) error
	SendNotification(ctx context.Context, req dtos.RequestForSendNotification) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Contact(ctx context.Context, req dtos.RequestForContact, file_names []string) error {
	var _file_names []string
	for _, file := range file_names {
		// TODO: change url later
		new := "https://api.waonapp.com" + "/bd928992-5e86-44c7-b625-5b580cb6ce2e/" + file
		_file_names = append(_file_names, new)
	}
	return s.repository.contact(ctx, req, _file_names)
}

func (s *service) GetSSS(ctx context.Context) ([]entities.SSS, error) {
	return s.repository.getSSS(ctx)
}

func (s *service) AddSSS(ctx context.Context, req dtos.RequestForSSS) error {
	return s.repository.addSSS(ctx, req)
}

func (s *service) SendNotification(ctx context.Context, req dtos.RequestForSendNotification) error {
	return s.repository.sendNotification(ctx, req)
}
