package license

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	customerrors "github.com/onwa/pkg/customErrors"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	getLastLicense(ctx context.Context, out *entities.License) error

	webhook(ctx context.Context, webhook_payload *entities.Webhook) error
	webhookForCrypto(ctx context.Context, invoice_id, order_id, payment_status string) error
	addLicense(ctx context.Context, req dtos.RequestForUpdateLicense) error

	getPresencesInfoForNotification(ctx context.Context) ([]dtos.ResponseForGetPresencesInfoForNotification, error)
	updateNotificationAfterOnlineForAllPresence(ctx context.Context, req dtos.RequestForUpdateNotificationAfterOnline) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) getLastLicense(ctx context.Context, out *entities.License) error {
	return r.db.WithContext(ctx).
		Model(&entities.License{}).
		Select("*,"+"end_date AT TIME ZONE ? AS end_date", state.CurrentTime(ctx)).
		Where("user_id = ?", state.GetCurrentUserID(ctx)).
		Order("created_at desc").
		First(out).Error
}

func (r *repository) addLicense(ctx context.Context, req dtos.RequestForUpdateLicense) error {
	var current_license entities.License

	tx := r.db.WithContext(ctx).Begin()

	if err := tx.Model(&entities.License{}).
		Where("user_id = ?", req.UserID).
		Order("created_at DESC").
		First(&current_license).Error; err != nil {
		tx.Rollback()
		return err
	}

	if req.Reason == 1 {
		var current_user_detail entities.UserDetail
		if err := tx.Model(&entities.UserDetail{}).
			Where("user_id = ?", current_license.UserID).
			First(&current_user_detail).Error; err != nil {
			tx.Rollback()
			return err
		}

		if current_user_detail.IsCommentDone {
			tx.Rollback()
			return errors.New(customerrors.ErrLicenseCommentAlreadyDone)
		}

		if current_license.LicenseType == 1 {
			if current_license.EndDate.Unix() < time.Now().Unix() {
				current_license.EndDate = time.Now().Add(time.Hour * time.Duration(req.Time))
			} else {
				current_license.EndDate = current_license.EndDate.Add(time.Hour * time.Duration(req.Time))
			}
		} else {
			if current_license.EndDate.Unix() < time.Now().Unix() {
				current_license.EndDate = time.Now().Add(time.Hour * time.Duration(req.Time))
			} else {
				current_license.EndDate = current_license.EndDate.Add(time.Hour * time.Duration(req.Time))
			}
		}

		if err := tx.Model(&entities.License{}).
			Where("user_id = ?", req.UserID).
			Where("id = ?", current_license.ID).
			Save(&current_license).Error; err != nil {
			tx.Rollback()
			return err
		}

		if err := tx.Model(&entities.UserDetail{}).
			Where("user_id = ?", current_license.UserID).
			Updates(map[string]interface{}{
				"is_comment_done": true,
			}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()
	return nil
}

func (r *repository) webhook(ctx context.Context, webhook_payload *entities.Webhook) error {
	var (
		current_user entities.User
		last_license entities.License
	)

	tx := r.db.WithContext(ctx).Begin()

	if err := tx.Model(&entities.User{}).
		Where("purchase_id = ?", webhook_payload.Event.AppUserID).
		First(&current_user).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Model(&entities.License{}).
		Where("user_id = ?", current_user.ID).
		Order("created_at desc").
		First(&last_license).Error; err != nil {
		tx.Rollback()
		return err
	}

	if webhook_payload.Event.Type == "INITIAL_PURCHASE" || webhook_payload.Event.Type == "RENEWAL" || webhook_payload.Event.Type == "NON_RENEWING_PURCHASE" {
		/*
			INITIAL_PURCHASE = ilk satın alma
			RENEWAL = yenileme
			NON_RENEWING_PURCHASE = yenileme olmayan satın alma
		*/

		var new_license entities.License

		new_license.UserID = current_user.ID
		new_license.LicenseType = 2

		time_n := time.Now()

		if last_license.LicenseType == 1 && last_license.TotalTime > 0 {
			time_n = time_n.Add(time.Hour * time.Duration(last_license.TotalTime))
		}

		if (last_license.LicenseType == 2) || (last_license.LicenseType == 3) || (last_license.LicenseType == 4) {
			if last_license.EndDate.Unix() > time_n.Unix() {
				time_n = last_license.EndDate
			}
		}

		new_license.TotalTime = 0
		new_license.PhoneNumberLimit = 3

		if strings.Contains(webhook_payload.Event.ProductID, entities.PRODUCT_ONE_MONTH) && (webhook_payload.Event.PresentedOfferingID == "iap") {
			new_license.EndDate = time_n.AddDate(0, 1, 0)
		} else if (strings.Contains(webhook_payload.Event.ProductID, entities.PRODUCT_ONE_MONTH_FREE)) && (strings.Contains(webhook_payload.Event.PeriodType, "TRIAL")) {
			new_license.EndDate = time_n.AddDate(0, 0, 3)
			new_license.LicenseType = 2
		} else if (strings.Contains(webhook_payload.Event.ProductID, entities.PRODUCT_ONE_MONTH_FREE)) && (strings.Contains(webhook_payload.Event.PeriodType, "NORMAL")) {
			new_license.EndDate = time_n.AddDate(0, 1, 0)
		} else if (strings.Contains(webhook_payload.Event.ProductID, entities.PRODUCT_ONE_WEEK_FREE)) && (strings.Contains(webhook_payload.Event.PeriodType, "TRIAL")) {
			new_license.EndDate = time_n.AddDate(0, 0, 3)
			new_license.LicenseType = 2
		} else if (strings.Contains(webhook_payload.Event.ProductID, entities.PRODUCT_ONE_WEEK_FREE)) && (strings.Contains(webhook_payload.Event.PeriodType, "NORMAL")) {
			new_license.EndDate = time_n.AddDate(0, 0, 7)
		} else if strings.Contains(webhook_payload.Event.ProductID, entities.PRODUCT_ONE_WEEK) && (webhook_payload.Event.PresentedOfferingID == "iap") {
			new_license.EndDate = time_n.AddDate(0, 0, 7)
		} else {
			new_license.EndDate = time_n.AddDate(0, 0, 1)
		}

		if err := tx.Model(&entities.License{}).
			Create(&new_license).Error; err != nil {
			tx.Rollback()
			return err
		}

	} else if webhook_payload.Event.Type == "CANCELLATION" || webhook_payload.Event.Type == "SUBSCRIPTION_PAUSED" || webhook_payload.Event.Type == "EXPIRATION" || webhook_payload.Event.Type == "BILLING_ISSUE" {
		/*
			CANCELLATION = iptal
			SUBSCRIPTION_PAUSED = yenileme olmayan satın alma
		*/
		var new_license entities.License

		new_license.UserID = current_user.ID
		new_license.LicenseType = 1
		new_license.TotalTime = 0
		new_license.PhoneNumberLimit = 3
		new_license.EndDate = last_license.EndDate

		if (last_license.LicenseType == 3) || (last_license.LicenseType == 4) {
			new_license.EndDate = time.Now()
		}

		if err := tx.Model(&entities.License{}).
			Create(&new_license).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()

	return nil
}

func (r *repository) webhookForCrypto(ctx context.Context, invoice_id, order_id, payment_status string) error {
	var (
		current_user   entities.User
		crypto_payment entities.CryptoPayment
		last_license   entities.License
	)

	if invoice_id != "" && payment_status == "finished" && order_id != "" {

		if err := r.db.WithContext(ctx).
			Model(&entities.CryptoPayment{}).
			Where("invoice_id = ?", invoice_id).
			Where("order_id = ?", order_id).
			First(&crypto_payment).Error; err != nil {
			return err
		}

		if crypto_payment.IsPaid {
			return fmt.Errorf("payment already paid")
		}

		// find user
		if err := r.db.WithContext(ctx).
			Model(&entities.User{}).
			Where("id = ?", crypto_payment.UserID).
			First(&current_user).Error; err != nil {
			return err
		}

		// find last license
		if err := r.db.WithContext(ctx).
			Model(&entities.License{}).
			Where("user_id = ?", current_user.ID).
			Order("created_at desc").
			First(&last_license).Error; err != nil {
			return err
		}

		time_n := time.Now()
		var new_license entities.License

		new_license.UserID = current_user.ID
		new_license.LicenseType = 2

		if last_license.LicenseType == 1 && last_license.TotalTime > 0 {
			time_n = time_n.Add(time.Hour * time.Duration(last_license.TotalTime))
		}

		if last_license.LicenseType == 2 && last_license.EndDate.Unix() > time_n.Unix() {
			time_n = last_license.EndDate
		}

		new_license.TotalTime = 0
		new_license.PhoneNumberLimit = 3

		if crypto_payment.LicenseType == 1 {
			new_license.EndDate = time_n.AddDate(0, 0, 7)
		} else if crypto_payment.LicenseType == 2 {
			new_license.EndDate = time_n.AddDate(0, 1, 0)
		} else {
			new_license.EndDate = time_n.AddDate(0, 0, 7)
		}

		tx := r.db.Begin()

		if err := tx.WithContext(ctx).
			Model(&entities.License{}).
			Create(&new_license).Error; err != nil {
			tx.Rollback()
			return err
		}

		crypto_payment.IsPaid = true

		if err := tx.WithContext(ctx).
			Model(&entities.CryptoPayment{}).
			Where("invoice_id = ?", invoice_id).
			Updates(map[string]interface{}{
				"is_paid": true,
			}).Error; err != nil {
			tx.Rollback()
			return err
		}

		tx.Commit()

	}

	return nil
}

// -----> Notification Start
func (r *repository) getPresencesInfoForNotification(ctx context.Context) ([]dtos.ResponseForGetPresencesInfoForNotification, error) {
	var (
		presences []entities.Presence
		resp      []dtos.ResponseForGetPresencesInfoForNotification
	)

	r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("user_id = ?", state.GetCurrentUserID(ctx)).
		Where("done = ?", false).
		Find(&presences)

	for _, v := range presences {
		var s dtos.ResponseForGetPresencesInfoForNotification

		s.ID = v.ID.String()
		s.SessionID = v.SessionID.String()
		s.UserID = v.UserID.String()
		s.ContactID = v.ContactID
		s.ContactName = v.ContactName
		s.Done = v.Done
		s.Local = v.Local
		s.NotificationAfterOffline = v.NotificationAfterOffline

		resp = append(resp, s)
	}

	return resp, nil
}

func (r *repository) updateNotificationAfterOnlineForAllPresence(ctx context.Context, req dtos.RequestForUpdateNotificationAfterOnline) error {

	for _, v := range req.Presences {
		r.db.Model(&entities.Presence{}).
			Where("id = ?", v.ID).
			Where("user_id = ?", state.GetCurrentUserID(ctx)).
			Updates(map[string]interface{}{
				"notification_after_offline": v.Data,
			})
	}

	return nil
}

// Notification End <-----
