package nowpayments

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"github.com/onwa/pkg/database"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/state"
	"github.com/onwa/pkg/utils"
)

func controlApi() bool {
	_, err := utils.RequestGETFORNOWPAYMENTS(NOWPAYMENTSURLFORSTATUS)
	if err != nil {
		return false
	}
	return true
}

func CreateInvoice(ctx context.Context, req dtos.RequestForCreateInvoice) (string, error) {
	var (
		price_amount       float64 = 0
		order_description  string  = ""
		new_crypto_payment entities.CryptoPayment
		order_id           string = uuid.New().String()
		resp_for_invoice   dtos.ResponseForCreateInvoice
		invoice_url        string
	)

	if !controlApi() {
		return invoice_url, errors.New("nowpayments api is not available")
	}

	// eth, doge, trx, sol, avax, fil
	// if req.PayCurrency != NOWPAYMENTSCURRENCYFORETH && req.PayCurrency != NOWPAYMENTSCURRENCYFORDOGE && req.PayCurrency != NOWPAYMENTSCURRENCYFORTRX && req.PayCurrency != NOWPAYMENTSCURRENCYFORSOL && req.PayCurrency != NOWPAYMENTSCURRENCYFORAVAX && req.PayCurrency != NOWPAYMENTSCURRENCYFORFIL {
	// 	return errors.New("pay currency must be eth, doge, trx, sol, avax, fil")
	// }

	if req.LicenseType == 1 || req.LicenseType == 2 {
		if req.LicenseType == 1 {
			price_amount = NOWPAYMENTSAMOUNTFORWEEKLY
			order_description = NOWPAYMENTSDESCRIPTIONFORWEEKLY
		} else {
			price_amount = NOWPAYMENTSAMOUNTFORMONTHLY
			order_description = NOWPAYMENTSDESCRIPTIONFORMONTHLY
		}
	} else {
		return invoice_url, errors.New("license must only be 1 or 2")
	}

	m := make(map[string]interface{})

	m["price_amount"] = price_amount
	m["price_currency"] = NOWPAYMENTSCURRENCYFORUSD // constant
	m["order_id"] = order_id
	m["order_description"] = order_description
	m["ipn_callback_url"] = "https://api.waonapp.com/api/v1/webhook/crypto"
	m["success_url"] = "https://waonapp.com/"
	m["cancel_url"] = "https://waonapp.com/"

	if req.PayCurrency != "" {
		m["pay_currency"] = req.PayCurrency
	}

	resp, err := utils.RequestPOSTFORNOWPAYMENTS(NOWPAYMENTSURLFORINVOICE, m)
	if err != nil {
		return invoice_url, err
	}

	if err := json.Unmarshal(*resp, &resp_for_invoice); err != nil {
		return invoice_url, err
	}

	invoice_url = resp_for_invoice.InvoiceURL

	new_crypto_payment.UserID = state.GetCurrentUserID(ctx)
	new_crypto_payment.InvoiceID = resp_for_invoice.ID
	new_crypto_payment.OrderID = order_id
	new_crypto_payment.OrderDescription = order_description
	new_crypto_payment.PriceAmount = price_amount
	new_crypto_payment.PriceCurrency = NOWPAYMENTSCURRENCYFORUSD
	new_crypto_payment.PayCurrency = req.PayCurrency
	new_crypto_payment.InvoiceURL = resp_for_invoice.InvoiceURL
	new_crypto_payment.IsPaid = false
	new_crypto_payment.LicenseType = req.LicenseType

	db := database.DBClient()
	if err := db.WithContext(ctx).
		Model(&entities.CryptoPayment{}).
		Create(&new_crypto_payment).Error; err != nil {
		return invoice_url, err
	}

	return invoice_url, nil
}
