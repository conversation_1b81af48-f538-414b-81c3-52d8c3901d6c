package state

import (
	"context"

	"github.com/google/uuid"
)

const (
	CurrentUserID         = "CurrentUserID"
	CurrentDeviceID       = "CurrentDeviceID"
	CurrentSessionID      = "CurrentSessionID"
	CurrentPushNotifToken = "CurrentPushNotifToken"
	CurrentPurchaseID     = "CurrentPurchaseID"
	CurrentRegID          = "CurrentRegID"
	CurrentPhoneNumber    = "CurrentPhoneNumber"
	CurrentUserIP         = "CurrentUserIP"
	CurrentTimezone       = "CurrentTimezone"
	CurrentPhoneLanguage  = "CurrentPhoneLanguage"
	CurrentOS             = "CurrentOS"
)

func GetCurrentUserID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentUserID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func GetCurrentSessionID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentSessionID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func GetCurrentDeviceID(ctx context.Context) string {
	value := ctx.Value(CurrentDeviceID)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentPushNotif(ctx context.Context) string {
	value := ctx.Value(CurrentPushNotifToken)
	if value == nil {
		return ""
	}
	return value.(string)
}

func CurrentPurchase(ctx context.Context) string {
	value := ctx.Value(CurrentPurchaseID)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentRegID(ctx context.Context) string {
	value := ctx.Value(CurrentRegID)
	if value == nil {
		return ""
	}
	return value.(string)
}

func CurrentPhone(ctx context.Context) string {
	value := ctx.Value(CurrentPhoneNumber)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentIP(ctx context.Context) string {
	value := ctx.Value(CurrentUserIP)
	if value == nil {
		return ""
	}
	return value.(string)
}

func CurrentTime(ctx context.Context) string {
	value := ctx.Value(CurrentTimezone)
	if value == nil {
		return ""
	}
	return value.(string)
}

func CurrentPhoneLang(ctx context.Context) string {
	value := ctx.Value(CurrentPhoneLanguage)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentOS(ctx context.Context) string {
	value := ctx.Value(CurrentOS)
	if value == nil {
		return ""
	}
	return value.(string)
}
