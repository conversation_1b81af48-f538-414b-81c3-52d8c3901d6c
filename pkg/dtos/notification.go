package dtos

type NotificationData struct {
	AppID                  string            `json:"app_id"`
	IncludeSubscriptionIDS []string          `json:"include_subscription_ids"`
	Headings               map[string]string `json:"headings"`
	Contents               map[string]string `json:"contents"`
}

type RequestForSendNotification struct {
	SessionID   string `json:"session_id"`
	PhoneNumber string `json:"phone_number"`
}

type RequestForUpdateNotificationAfterOnline struct {
	Presences []struct {
		ID   string `json:"id"`
		Data bool   `json:"data"`
	} `json:"presences"`
}

type ResponseForGetPresencesInfoForNotification struct {
	ID                       string `json:"id"`
	SessionID                string `json:"session_id"`
	UserID                   string `json:"user_id"`
	ContactID                string `json:"contact_id"`
	ContactName              string `json:"contact_name"`
	Done                     bool   `json:"done"`
	Local                    string `json:"local"`
	NotificationAfterOffline bool   `json:"notification_after_offline"`
}
