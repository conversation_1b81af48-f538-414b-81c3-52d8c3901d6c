package dtos

type RequestForWPCreate struct {
	Content string `json:"content"`
}

type RequestForWPCode struct {
	E164PhoneNumber string `json:"e_164_phone_number" validate:"required" example:"+48573253866"`
}

type RequestForCreateSession struct {
	E164PhoneNumber string `json:"e_164_phone_number" validate:"required" example:"+48573253866"`
}

type RequestForWPPhoneNumbers struct {
	PhoneNumbers []struct {
		Name           string `json:"name" example:"samet"`
		DialCode       string `json:"dial_code" validate:"required" example:"90"`
		PhoneNumber    string `json:"phone_number" validate:"required" example:"+48573253866"`
		RawPhoneNumber string `json:"raw_phone_number" validate:"required"`
	} `json:"phone_numbers" validate:"required"`
}

type RequestForProfilePhoto struct {
	Phone      string `json:"phone" example:"48573253866"`
	ForSession bool   `json:"for_session" example:"true"`
}

type ResponseForSessionCreate struct {
	SessionID string `json:"session_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type ResponseForCheckDeviceWithSession struct {
	Status      string `json:"status"`
	SessionID   string `json:"session_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PhoneNumber string `json:"phone_number" example:"+48573253866"`
}

type ResponseForPresenceStart struct {
	PresenceID string `json:"presence_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Status     string `json:"status"`
}

type ResponseForPresenceGetLast struct {
	PresenceID string `json:"presence_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SessionID  string `json:"session_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Status     string `json:"status"`
	LastSeen   string `json:"last_seen"`
}
