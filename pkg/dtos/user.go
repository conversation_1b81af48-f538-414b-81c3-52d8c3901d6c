package dtos

import "time"

type ResponseForGetCurrentUser struct {
	TimeZone                string    `json:"time_zone"`
	E164PhoneNumber         string    `json:"e_164_phone_number"`
	SessionID               string    `json:"session_id"`
	PushNotifToken          string    `json:"push_notif_token"`
	DeviceID                string    `json:"device_id"`
	PhoneLanguage           string    `json:"phone_language"`
	OS                      string    `json:"os"`
	LastVersionName         string    `json:"last_version_name"`
	LastVersionBuildNumber  int       `json:"last_version_build_number"`
	LastLicenseControlled   bool      `json:"last_license_controlled"`
	LastConnectionStatus    string    `json:"last_connection_status"`
	LastConnectionCheckTime time.Time `json:"last_connection_check_time"`
}
