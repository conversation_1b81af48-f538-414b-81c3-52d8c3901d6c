package mail

import (
	"log"

	"github.com/onwa/pkg/config"
	mail "github.com/xhit/go-simple-mail"
)

var (
	password = config.InitConfig().Smtp.Password
	host     = config.InitConfig().Smtp.Host
	port     = config.InitConfig().Smtp.Port
	sender   = config.InitConfig().Smtp.Sender
)

func MailClient() *mail.SMTPClient {
	server := mail.NewSMTPClient()
	server.Host = host
	server.Port = port
	server.Username = sender
	server.Password = password
	server.Encryption = mail.EncryptionTLS
	smtpClient, err := server.Connect()
	if err != nil {
		log.Println("MailClient Error: ", err.Error())
	}
	return smtpClient
}
