package entities

type Webhook struct {
	APIVersion string `json:"api_version"`
	Event      Event  `json:"event"`
}

type Event struct {
	Aliases                  []string             `json:"aliases"`
	AppID                    string               `json:"app_id"`
	AppUserID                string               `json:"app_user_id"`
	CancelReason             string               `json:"cancel_reason"`
	CommissionPercentage     float64              `json:"commission_percentage"`
	CountryCode              string               `json:"country_code"`
	Currency                 string               `json:"currency"`
	EntitlementID            interface{}          `json:"entitlement_id"`
	EntitlementIDS           interface{}          `json:"entitlement_ids"`
	Environment              string               `json:"environment"`
	EventTimestampMS         int64                `json:"event_timestamp_ms"`
	ExpirationAtMS           int64                `json:"expiration_at_ms"`
	ID                       string               `json:"id"`
	IsFamilyShare            bool                 `json:"is_family_share"`
	OfferCode                interface{}          `json:"offer_code"`
	OriginalAppUserID        string               `json:"original_app_user_id"`
	OriginalTransactionID    string               `json:"original_transaction_id"`
	PeriodType               string               `json:"period_type"`
	PresentedOfferingID      string               `json:"presented_offering_id"`
	Price                    float64              `json:"price"`
	PriceInPurchasedCurrency float64              `json:"price_in_purchased_currency"`
	ProductID                string               `json:"product_id"`
	PurchasedAtMS            int64                `json:"purchased_at_ms"`
	RenewalNumber            int64                `json:"renewal_number"`
	Store                    string               `json:"store"`
	SubscriberAttributes     SubscriberAttributes `json:"subscriber_attributes"`
	TakehomePercentage       float64              `json:"takehome_percentage"`
	TaxPercentage            float64              `json:"tax_percentage"`
	TransactionID            string               `json:"transaction_id"`
	Type                     string               `json:"type"`
}

type SubscriberAttributes struct {
}
