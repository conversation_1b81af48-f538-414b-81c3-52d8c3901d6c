package entities

import "github.com/google/uuid"

type Log struct {
	Base
	Title     string    `json:"title" example:"example title"`
	Message   string    `json:"message" example:"order created"`
	RegID     string    `json:"reg_id"`
	Type      string    `json:"type" example:"info"`
	Ip        string    `json:"ip" example:"127.0.0.1"`
	URL       string    `json:"url"`
	OS        string    `json:"os"`
	UserID    uuid.UUID `json:"user_id" gorm:"default:null;type:uuid"`
	DeviceID  string    `json:"device_id"`
	SessionID uuid.UUID `json:"session_id" gorm:"default:null;type:uuid"`
}
