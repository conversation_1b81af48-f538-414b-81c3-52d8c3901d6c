package entities

import "github.com/google/uuid"

type CryptoPayment struct {
	Base

	UserID           uuid.UUID `json:"user_id"`
	InvoiceID        string    `json:"invoice_id"`
	OrderID          string    `json:"order_id"`
	OrderDescription string    `json:"order_description"`
	PriceAmount      float64   `json:"price_amount"`
	PriceCurrency    string    `json:"price_currency"`
	PayCurrency      string    `json:"pay_currency"`
	InvoiceURL       string    `json:"invoice_url"`
	IsPaid           bool      `json:"is_paid"`
	LicenseType      int       `json:"license_type"` // 1: weekly, 2: monthly
}
