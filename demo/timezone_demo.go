package main

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/middleware"
	"github.com/onwa/pkg/state"
)

func main() {
	gin.SetMode(gin.ReleaseMode)

	// Create a new Gin router
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Apply timezone converter middleware to API routes
	api := router.Group("/api/v1")
	api.Use(middleware.TimezoneConverter())

	// Test endpoint that returns time data
	api.GET("/test-timezone", func(c *gin.Context) {
		// Simulate setting user timezone from header or JWT
		timezone := c.<PERSON>eader("X-Timezone")
		if timezone == "" {
			timezone = "Europe/Istanbul" // Default timezone for demo
		}
		c.Set(state.CurrentTimezone, timezone)

		// Return response with various time fields
		c.JSON(200, gin.H{
			"message": "Timezone conversion demo",
			"data": gin.H{
				"current_time": time.Now().UTC().Format(time.RFC3339),
				"created_at":   "2024-01-01T12:00:00Z",
				"updated_at":   "2024-01-01T15:30:00Z",
				"last_login":   "2024-01-01T09:15:00Z",
				"nested_data": gin.H{
					"timestamp":      "2024-01-01T18:45:00Z",
					"expire_time":    "2024-12-31T23:59:59Z",
					"non_time_field": "this should not change",
				},
				"time_array": []gin.H{
					{"event_time": "2024-01-01T10:00:00Z", "name": "Event 1"},
					{"event_time": "2024-01-01T14:00:00Z", "name": "Event 2"},
				},
			},
			"user_timezone": timezone,
			"status":        200,
		})
	})

	// Test endpoint without timezone (should not convert)
	api.GET("/test-no-timezone", func(c *gin.Context) {
		// Don't set timezone - should not convert times
		c.JSON(200, gin.H{
			"message": "No timezone conversion",
			"data": gin.H{
				"current_time": time.Now().UTC().Format(time.RFC3339),
				"created_at":   "2024-01-01T12:00:00Z",
			},
			"status": 200,
		})
	})

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "OK",
			"time":   time.Now().UTC().Format(time.RFC3339),
		})
	})

	// Start server
	router.Run(":8080")
}
